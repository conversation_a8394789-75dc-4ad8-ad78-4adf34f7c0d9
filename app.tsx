import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Main App Component
const App = () => {
  // State variables for UI elements and data
  const [isDragging, setIsDragging] = useState(false); // Tracks if an image is being dragged over the drop zone
  const [imagePreview, setImagePreview] = useState(null); // Stores the URL of the dropped image for preview
  const [useTextarea, setUseTextarea] = useState(false); // Toggles between image drag-and-drop and text area input
  const [messageInput, setMessageInput] = useState(''); // Stores the user's typed message
  const [temperature, setTemperature] = useState(0.5); // Controls the AI's response "temperature" (creativity/randomness)
  const [isLoading, setIsLoading] = useState(false); // Indicates if the AI is generating a response
  const [aiResponse, setAiResponse] = useState(''); // Stores the full AI-generated response
  const [displayedResponse, setDisplayedResponse] = useState(''); // Stores the response as it's being "typed"
  const [showResultCard, setShowResultCard] = useState(false); // Controls visibility of the result card
  const [isCopied, setIsCopied] = useState(false); // Indicates if the AI response has been copied to clipboard

  // Refs for DOM elements
  const temperatureSliderRef = useRef(null); // Ref for the temperature slider to attach event listeners
  const fileInputRef = useRef(null); // Ref for the hidden file input element

  // --- SEO Optimization: Meta Tags and Structured Data ---
  useEffect(() => {
    // Set document title
    document.title = "How to Reply? 🤔 Генератор ответов на сообщения от ИИ";

    // Function to create or update a meta tag
    const updateMeta = (name, content, property = false) => {
      let tag = document.querySelector(`meta[${property ? 'property' : 'name'}="${name}"]`);
      if (!tag) {
        tag = document.createElement('meta');
        tag.setAttribute(property ? 'property' : 'name', name);
        document.head.appendChild(tag);
      }
      tag.setAttribute('content', content);
    };

    // Update standard meta tags
    updateMeta('description', 'Не знаете, что ответить? Загрузите скриншот чата или введите сообщение, и наш ИИ сгенерирует идеальный ответ. Быстро, понятно, стильно.');
    updateMeta('keywords', 'ИИ, чат, ответ, генератор, сообщения, скриншот, помощь, общение, AI, chat, reply, generator, messages, screenshot, communication');

    // Open Graph tags for social media sharing
    updateMeta('og:title', 'How to Reply? 🤔 Генератор ответов на сообщения от ИИ', true);
    updateMeta('og:description', 'Не знаете, что ответить? Загрузите скриншот чата или введите сообщение, и наш ИИ сгенерирует идеальный ответ. Быстро, понятно, стильно.', true);
    updateMeta('og:type', 'website', true);
    updateMeta('og:url', window.location.href, true); // Dynamic URL
    updateMeta('og:image', 'https://placehold.co/1200x630/3AAFF0/FFFFFF?text=How+to+Reply%3F', true); // Placeholder image for social sharing
    updateMeta('og:image:width', '1200', true);
    updateMeta('og:image:height', '630', true);

    // Twitter Card tags
    updateMeta('twitter:card', 'summary_large_image');
    updateMeta('twitter:title', 'How to Reply? 🤔 Генератор ответов на сообщения от ИИ');
    updateMeta('twitter:description', 'Не знаете, что ответить? Загрузите скриншот чата или введите сообщение, и наш ИИ сгенерирует идеальный ответ. Быстро, понятно, стильно.');
    updateMeta('twitter:image', 'https://placehold.co/1200x630/3AAFF0/FFFFFF?text=How+to+Reply%3F'); // Placeholder image for Twitter

    // Structured Data (JSON-LD) for WebPage
    const jsonLd = {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "How to Reply? 🤔 Генератор ответов на сообщения от ИИ",
      "description": "Онлайн-инструмент для генерации ответов на сообщения в чатах с помощью искусственного интеллекта. Просто загрузите скриншот или введите текст, и получите идеальный ответ.",
      "url": window.location.href,
      "image": "https://placehold.co/1200x630/3AAFF0/FFFFFF?text=How+to+Reply%3F", // Same placeholder image
      "publisher": {
        "@type": "Organization",
        "name": "How to Reply?" // Replace with your organization name if applicable
      }
    };

    let scriptTag = document.querySelector('script[type="application/ld+json"]');
    if (!scriptTag) {
      scriptTag = document.createElement('script');
      scriptTag.type = 'application/ld+json';
      document.head.appendChild(scriptTag);
    }
    scriptTag.textContent = JSON.stringify(jsonLd);

    // Cleanup function to remove tags if component unmounts (important for SPA)
    return () => {
      document.title = "React App"; // Reset to default or previous title
      document.querySelectorAll('meta[name^="description"], meta[name^="keywords"], meta[property^="og:"], meta[name^="twitter:"]').forEach(tag => tag.remove());
      if (scriptTag) scriptTag.remove();
    };
  }, []); // Empty dependency array means this effect runs once on mount and cleans up on unmount

  // --- Drag-and-Drop Handlers ---
  const handleDragOver = (e) => {
    e.preventDefault(); // Prevent default to allow drop
    setIsDragging(true); // Set dragging state for visual feedback
  };

  const handleDragLeave = () => {
    setIsDragging(false); // Reset dragging state
  };

  const handleDrop = (e) => {
    e.preventDefault(); // Prevent default behavior (prevent file from being opened)
    setIsDragging(false); // Reset dragging state
    const file = e.dataTransfer.files[0]; // Get the dropped file
    if (file && file.type.startsWith('image/')) { // Check if it's an image
      const reader = new FileReader(); // Create a FileReader instance
      reader.onloadend = () => {
        setImagePreview(reader.result); // Set the image preview URL
      };
      reader.readAsDataURL(file); // Read the file as a data URL
    }
  };

  // Handle file input change (when user clicks the drop zone and selects a file)
  const handleFileInputChange = (e) => {
    const file = e.target.files[0]; // Get the selected file
    if (file && file.type.startsWith('image/')) { // Check if it's an image
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result); // Set the image preview URL
      };
      reader.readAsDataURL(file);
    }
  };

  // --- AI Reply Generation using Gemini API ---
  const getReply = async () => {
    setIsLoading(true); // Show loading spinner
    setAiResponse(''); // Clear previous AI response
    setDisplayedResponse(''); // Clear displayed response
    setShowResultCard(false); // Hide result card initially

    let chatHistory = [];
    let prompt = "";
    let payload = {};

    // Construct prompt and payload based on input type (image or text)
    if (imagePreview && !useTextarea) {
      // If an image is provided, create a multimodal prompt
      prompt = `Проанализируй этот скриншот чата и предложи подходящий ответ на последнее сообщение. Сделай ответ в соответствии с температурой: ${temperature.toFixed(2)}.`;
      chatHistory.push({
        role: "user",
        parts: [
          { text: prompt },
          {
            inlineData: {
              mimeType: "image/png", // Assuming PNG, adjust if other types are supported
              data: imagePreview.split(',')[1] // Extract base64 data part
            }
          }
        ]
      });
      payload = { contents: chatHistory };
    } else if (messageInput && useTextarea) {
      // If text is provided, create a text-only prompt
      prompt = `Предложи подходящий ответ на следующее сообщение: '${messageInput}'. Сделай ответ в соответствии с температурой: ${temperature.toFixed(2)}.`;
      chatHistory.push({ role: "user", parts: [{ text: prompt }] });
      payload = { contents: chatHistory };
    } else {
      // No valid input
      setAiResponse("Пожалуйста, загрузите скриншот или введите сообщение.");
      setIsLoading(false);
      setShowResultCard(true);
      return;
    }

    try {
      const apiKey = ""; // API key will be provided by Canvas runtime
      const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      const result = await response.json();

      if (result.candidates && result.candidates.length > 0 &&
        result.candidates[0].content && result.candidates[0].content.parts &&
        result.candidates[0].content.parts.length > 0) {
        const text = result.candidates[0].content.parts[0].text;
        setAiResponse(text); // Set the AI-generated response
      } else {
        console.error("Unexpected API response structure:", result);
        setAiResponse("Извините, не удалось сгенерировать ответ. Попробуйте еще раз.");
      }
    } catch (error) {
      console.error("Error calling Gemini API:", error);
      setAiResponse("Произошла ошибка при получении ответа. Пожалуйста, проверьте подключение к интернету или попробуйте позже.");
    } finally {
      setIsLoading(false); // Hide loading spinner
      setShowResultCard(true); // Show the result card
    }
  };

  // --- Typewriter Effect for AI Response ---
  useEffect(() => {
    if (aiResponse) {
      let i = 0;
      // Set an interval to add characters one by one
      const typingInterval = setInterval(() => {
        setDisplayedResponse((prev) => prev + aiResponse.charAt(i));
        i++;
        if (i === aiResponse.length) {
          clearInterval(typingInterval); // Stop typing when all characters are displayed
        }
      }, 30); // Typing speed in milliseconds per character
      return () => clearInterval(typingInterval); // Cleanup interval on component unmount or aiResponse change
    }
  }, [aiResponse]); // Rerun effect when aiResponse changes

  // --- Haptic Feedback for Temperature Slider ---
  useEffect(() => {
    const slider = temperatureSliderRef.current;
    if (!slider) return;

    const handleHaptic = () => {
      // Vibrate on specific thresholds (0.25, 0.5, 0.75) for tactile feedback
      if (
        (temperature >= 0.24 && temperature <= 0.26) || // Cold zone
        (temperature >= 0.49 && temperature <= 0.51) || // Neutral zone
        (temperature >= 0.74 && temperature <= 0.76)    // Hot zone
      ) {
        if (navigator.vibrate) { // Check if Vibration API is supported
          navigator.vibrate(50); // Short vibration (50ms)
        }
      }
    };

    // Attach event listener to the slider for input changes
    slider.addEventListener('input', handleHaptic);
    return () => slider.removeEventListener('input', handleHaptic); // Cleanup
  }, [temperature]); // Rerun effect when temperature changes

  // --- Copy to Clipboard Functionality ---
  const copyToClipboard = () => {
    // Using document.execCommand('copy') for better iframe compatibility in some environments
    const textArea = document.createElement('textarea');
    textArea.value = aiResponse; // Set the text to be copied
    document.body.appendChild(textArea); // Append to DOM temporarily
    textArea.select(); // Select the text
    try {
      document.execCommand('copy'); // Execute copy command
      setIsCopied(true); // Show "Copied!" feedback
      setTimeout(() => setIsCopied(false), 2000); // Reset copied state after 2 seconds
    } catch (err) {
      console.error('Failed to copy text: ', err); // Log any errors
    }
    document.body.removeChild(textArea); // Remove the temporary textarea
  };

  // --- Reset Form Function ---
  const resetForm = () => {
    setImagePreview(null); // Clear image preview
    setMessageInput(''); // Clear text input
    setTemperature(0.5); // Reset temperature to neutral
    setIsLoading(false); // Stop loading
    setAiResponse(''); // Clear AI response
    setDisplayedResponse(''); // Clear displayed response
    setShowResultCard(false); // Hide result card
    setIsCopied(false); // Reset copied state
    setUseTextarea(false); // Reset input toggle to drag-and-drop
  };

  // --- Kinetic H1 Animation Variants (Framer Motion) ---
  const letterVariants = {
    initial: { y: 0 }, // Initial position
    hover: { y: [-2, 2, -2, 2, 0], transition: { duration: 0.5, ease: "easeInOut" } }, // Drift effect on hover
    tap: { y: [-2, 2, -2, 2, 0], transition: { duration: 0.5, ease: "easeInOut" } }, // Drift effect on tap
  };

  return (
    // Main container with responsive dark/light theme and font
    <div className="min-h-screen bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-50 font-sans transition-colors duration-300">
      {/* Container for content, centered and max-width on larger screens */}
      <div className="container mx-auto p-4 md:p-8 lg:max-w-4xl xl:max-w-5xl">
        {/* Hero Section - Full viewport height on initial load */}
        <section className="min-h-screen flex flex-col items-center justify-center text-center py-16">
          {/* Emoji Logo */}
          <motion.div
            className="text-6xl mb-6"
            initial={{ opacity: 0, y: -20 }} // Initial animation state
            animate={{ opacity: 1, y: 0 }}   // Animation to
            transition={{ duration: 0.5, delay: 0.2 }} // Animation properties
          >
            🤔➡️💬
          </motion.div>

          {/* H1: Kinetic Typography - Each letter is a motion.span */}
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-extrabold mb-4 leading-tight">
            {"Не знаете, что ответить?".split("").map((char, index) => (
              <motion.span
                key={index}
                variants={letterVariants}
                whileHover="hover" // Apply hover animation
                whileTap="tap"   // Apply tap animation
                className="inline-block cursor-pointer" // Make each letter clickable
              >
                {char === " " ? "\u00A0" : char} {/* Preserve spaces with non-breaking space */}
              </motion.span>
            ))}
          </h1>

          {/* Subhead */}
          <motion.p
            className="text-lg md:text-xl text-gray-600 dark:text-gray-400 mb-12 max-w-md"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            Вставьте чат или напечатайте, ИИ сделает остальное.
          </motion.p>

          <div className="w-full max-w-md mb-8">
            {/* AnimatePresence for smooth transitions between drag-and-drop and textarea */}
            <AnimatePresence mode="wait">
              {!useTextarea ? (
                // Drag-and-Drop Zone
                <motion.div
                  key="drag-drop-zone"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className={`relative flex flex-col items-center justify-center p-8 border-2 ${isDragging ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-dashed border-gray-300 dark:border-gray-700'} rounded-xl cursor-pointer transition-all duration-200 h-48`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                  onClick={() => fileInputRef.current.click()} // Trigger hidden file input on click
                  aria-label="Перетащите скриншот или нажмите, чтобы загрузить" // ARIA label for accessibility
                  role="button" // Indicate it's a clickable element
                  tabIndex="0" // Make it focusable
                >
                  <input
                    id="fileInput"
                    type="file"
                    accept="image/*" // Only accept image files
                    className="hidden" // Hide the default input
                    onChange={handleFileInputChange}
                    ref={fileInputRef} // Attach ref
                  />
                  {imagePreview ? (
                    // Display image preview if available
                    <img src={imagePreview} alt="Предварительный просмотр скриншота" className="max-h-full max-w-full object-contain rounded-lg" />
                  ) : (
                    // Default state with icon and text
                    <>
                      {/* Lucide Mail Icon (inline SVG for self-containment) */}
                      <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400 dark:text-gray-500 mb-4">
                        <rect x="2" y="4" width="20" height="16" rx="2" />
                        <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                      </svg>
                      <p className="text-gray-500 dark:text-gray-400 text-sm md:text-base">Перетащите скриншот или нажмите</p>
                    </>
                  )}
                </motion.div>
              ) : (
                // Text Area Input
                <motion.textarea
                  key="text-area"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="w-full p-4 border border-gray-300 dark:border-gray-700 rounded-xl bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-50 placeholder-gray-400 dark:placeholder-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-y min-h-[12rem]"
                  placeholder="Вставьте входящее сообщение..."
                  value={messageInput}
                  onChange={(e) => setMessageInput(e.target.value)}
                  aria-label="Введите сообщение для ответа ИИ" // ARIA label for text area
                ></motion.textarea>
              )}
            </AnimatePresence>

            {/* Toggle Switch for input method */}
            <div className="flex items-center justify-center mt-4">
              <label htmlFor="toggleTextarea" className="flex items-center cursor-pointer">
                <div className="relative">
                  <input
                    type="checkbox"
                    id="toggleTextarea"
                    className="sr-only" // Screen reader only
                    checked={useTextarea}
                    onChange={() => setUseTextarea(!useTextarea)}
                    aria-label="Переключить между вводом скриншота и текстовым вводом" // ARIA label for toggle
                  />
                  {/* Background track of the toggle */}
                  <div className="block bg-gray-600 w-14 h-8 rounded-full"></div>
                  {/* Movable dot/thumb of the toggle */}
                  <div className={`dot absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition ${useTextarea ? 'translate-x-full bg-gradient-to-r from-[#3AAFF0] to-[#FF6F61]' : ''}`}></div>
                </div>
                <span className="ml-3 text-gray-700 dark:text-gray-300 text-sm">Предпочитаю текст</span>
              </label>
            </div>
          </div>
        </section>

        {/* Main Content Area - Layout changes to two columns on larger screens */}
        <div className="lg:grid lg:grid-cols-2 lg:gap-8 xl:gap-12 py-16">
          {/* Input Controls Column (Left on larger screens) */}
          {/* Applied max-w-md and mx-auto to center and constrain content on larger screens */}
          <div className="lg:order-1 mb-12 lg:mb-0 w-full lg:max-w-md lg:mx-auto">
            {/* Temperature Control Section */}
            <div className="mb-12">
              <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Настройте тон ответа</h2>
              <div className="relative w-full h-12 rounded-full overflow-hidden mb-4">
                {/* Range Slider */}
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.05"
                  value={temperature}
                  onChange={(e) => setTemperature(parseFloat(e.target.value))}
                  ref={temperatureSliderRef} // Attach ref for haptic feedback
                  className="absolute z-10 w-full h-full appearance-none bg-transparent cursor-pointer
                             [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-6 [&::-webkit-slider-thumb]:h-6 [&::-webkit-slider-thumb]:bg-white [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:shadow-lg [&::-webkit-slider-thumb]:border-2 [&::-webkit-slider-thumb]:border-gray-200 dark:[&::-webkit-slider-thumb]:border-gray-700
                             [&::-moz-range-thumb]:w-6 [&::-moz-range-thumb]:h-6 [&::-moz-range-thumb]:bg-white [&::-moz-range-thumb]:rounded-full [&::-moz-range-thumb]:shadow-lg [&::-moz-range-thumb]:border-2 [&::-moz-range-thumb]:border-gray-200 dark:[&::-moz-range-thumb]:border-gray-700
                             focus:outline-none focus:ring-2 focus:ring-blue-500"
                  aria-label={`Температура ответа ИИ: ${temperature.toFixed(2)}`} // ARIA label for slider
                />
                {/* Gradient background for the slider track */}
                <div
                  className="absolute inset-0 rounded-full"
                  style={{
                    background: `linear-gradient(to right, #3AAFF0, #FF6F61)`,
                  }}
                ></div>
                {/* Tooltip for current temperature value */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <motion.div
                    className="absolute -top-8 bg-gray-800 text-white text-xs px-2 py-1 rounded-md shadow-lg"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.1 }}
                    // Position tooltip dynamically based on slider value
                    style={{ left: `calc(${temperature * 100}% - 1rem)` }}
                  >
                    {temperature.toFixed(2)}
                  </motion.div>
                </div>
              </div>
              {/* Labels for temperature zones */}
              <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mt-2">
                <span>Холодно</span>
                <span>Нейтрально</span>
                <span>Горячо</span>
              </div>
            </div>

            {/* Primary Call to Action Button */}
            <motion.button
              onClick={getReply} // Trigger AI reply generation
              className="w-full py-4 px-6 rounded-xl text-white font-bold text-lg focus:outline-none focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-700 transition-all duration-300 shadow-lg"
              style={{
                background: `linear-gradient(to right, #3AAFF0, #FF6F61)`, // Apply accent gradient
              }}
              whileHover={{ scale: 1.02 }} // Subtle hover effect
              whileTap={{ scale: 0.98 }}   // Subtle tap effect
              disabled={isLoading || (!imagePreview && !messageInput)} // Disable if loading or no input
              aria-label={isLoading ? "Генерируется ответ" : "Получить ответ ИИ"} // ARIA label for button
            >
              {isLoading ? (
                // Loading spinner
                <motion.span
                  className="inline-block w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"
                  role="status" // Indicate it's a status message for screen readers
                  aria-hidden="true" // Hide from screen readers as role="status" is sufficient
                ></motion.span>
              ) : (
                "Получить ответ ✨" // Button text with sparkle emoji
              )}
            </motion.button>
          </div>

          {/* Result Card Column (Right on larger screens) */}
          <div className="lg:order-2">
            {/* AnimatePresence for smooth transitions between skeleton loader and result card */}
            <AnimatePresence mode="wait">
              {isLoading && (
                // Skeleton Loader while AI is processing
                <motion.div
                  key="skeleton"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 animate-pulse"
                  aria-label="Загрузка ответа ИИ" // ARIA label for loading state
                >
                  <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full mb-2"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6 mb-2"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                  <div className="flex justify-end mt-8">
                    <div className="h-10 w-24 bg-gray-200 dark:bg-gray-700 rounded-lg mr-2"></div>
                    <div className="h-10 w-32 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                  </div>
                </motion.div>
              )}

              {showResultCard && !isLoading && (
                // Result Card displaying AI-generated reply
                <motion.div
                  key="result-card"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-md border border-gray-200 dark:border-gray-700"
                  role="region" // Define a landmark region for screen readers
                  aria-live="polite" // Announce updates politely
                  aria-label="Сгенерированный ответ ИИ" // ARIA label for the result card
                >
                  <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Ваш ответ:</h2>
                  {/* Displayed AI response with typewriter effect */}
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-6 whitespace-pre-wrap">
                    {displayedResponse}
                  </p>
                  <div className="flex flex-col sm:flex-row justify-end gap-3">
                    {/* Copy Button */}
                    <motion.button
                      onClick={copyToClipboard}
                      className="flex-1 sm:flex-none py-3 px-6 rounded-lg border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-400"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      aria-label="Скопировать сгенерированный ответ в буфер обмена" // ARIA label for copy button
                    >
                      {isCopied ? "Скопировано! 🎉" : "Копировать"}
                    </motion.button>
                    {/* "Another Idea" Button */}
                    <motion.button
                      onClick={getReply} // Re-run AI for a new response
                      className="flex-1 sm:flex-none py-3 px-6 rounded-lg bg-gradient-to-r from-[#3AAFF0] to-[#FF6F61] text-white font-medium hover:opacity-90 transition-opacity duration-200 focus:outline-none focus:ring-2 focus:ring-blue-300 dark:focus:ring-blue-700"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      aria-label="Сгенерировать еще одну идею ответа" // ARIA label for another idea button
                    >
                      Ещё идея ✨
                    </motion.button>
                  </div>
                  {/* Ethics Note */}
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-4 text-right">
                    Звучит нормально? Используйте критическое мышление.
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Reset Button (positioned fixed at the bottom for easy access) */}
        <div className="fixed bottom-4 left-1/2 -translate-x-1/2 z-50">
          <motion.button
            onClick={resetForm} // Trigger form reset
            className="bg-gray-800 dark:bg-gray-200 text-white dark:text-gray-900 px-6 py-3 rounded-full shadow-lg text-sm font-medium hover:bg-gray-700 dark:hover:bg-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-400"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            aria-label="Сбросить форму" // ARIA label for reset button
          >
            Сбросить форму
          </motion.button>
        </div>
      </div>
    </div>
  );
};

export default App;
